"""
Enhanced business logic services for Case Management with workflow automation.

This module contains service classes that encapsulate business rules and operations,
keeping views and forms focused on presentation and validation respectively.
"""
import logging
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from django.db.models import Q
from django.contrib.auth import get_user_model

# from notifications.models import Notification # Temporarily commented out due to migration issues
from .models import Case, Task, WorkflowStage, StageHistory, Department, Tooth
from common.workflow_automation import workflow_automation, TransitionContext
from common.exceptions import WorkflowError, BusinessRuleViolation
from common.validators import CentralizedValidationService

logger = logging.getLogger(__name__)

User = get_user_model()


class CaseService:
    """Encapsulates business logic for Case operations."""

    @staticmethod
    def get_dentist_cases(user):
        """Get cases for a dentist user."""
        if not hasattr(user, 'dentist_profile'):
            return Case.objects.none()

        return Case.objects.filter(
            Q(dentist=user.dentist_profile) |
            Q(dentist_user=user)
        ).order_by('-received_date_time')

    @staticmethod
    def update_case_status(case, new_status, user=None, reason=None, force=False):
        """
        Update a case's status with workflow automation and proper validation.

        Args:
            case: The Case instance to update
            new_status: The new status value
            user: The user making the change (for audit)
            reason: Reason for the status change
            force: Whether to force the transition (bypass some validations)

        Returns:
            (success, message, updated_case) tuple
        """
        old_status = case.status
        if old_status == new_status:
            return True, "Status unchanged", case

        try:
            # Use workflow automation for status transitions
            context = TransitionContext(
                user=user,
                reason=reason or f"Manual status change by {user}",
                force=force,
                notify_stakeholders=True
            )

            result, message = workflow_automation.transition_entity('case', case, new_status, context)

            if result.value == 'success':
                logger.info(f"Case #{case.case_number} status changed from {old_status} to {new_status}")

                # Create notifications for relevant users
                if user and user != case.dentist_user:
                    logger.info(f"Would create notification for user {case.dentist_user} about case status change")

                return True, message, case
            else:
                logger.warning(f"Case status transition failed: {message}")
                return False, message, case

        except WorkflowError as e:
            logger.error(f"Workflow error during case status update: {e}")
            return False, str(e), case
        except Exception as e:
            logger.error(f"Unexpected error during case status update: {e}", exc_info=True)
            return False, f"System error: {str(e)}", case

    @staticmethod
    def change_case_department(case, new_department, user=None):
        """Change a case's responsible department with proper logging."""
        if case.responsible_department == new_department:
            return case

        old_department = case.responsible_department
        case.responsible_department = new_department

        # If we're changing departments, we might need to update the current stage
        if new_department:
            # Get the first stage for this department in the workflow
            if case.workflow_template:
                first_stage = case.workflow_template.stages.filter(
                    department=new_department
                ).order_by('order').first()

                if first_stage:
                    case.current_stage = first_stage

        case.save(update_fields=['responsible_department', 'current_stage'])

        # Log the change
        logger.info(
            f"Case #{case.case_number} department changed from "
            f"{old_department} to {new_department} by {user or 'system'}"
        )

        return case

    @staticmethod
    def assign_teeth_to_case(case, teeth_numbers):
        """
        Assign teeth to a case by their numbers.

        Args:
            case: The Case instance
            teeth_numbers: List of int tooth numbers

        Returns:
            Updated Case instance
        """
        with transaction.atomic():
            # Clear existing teeth
            case.selected_teeth.clear()

            # Add new teeth
            for number in teeth_numbers:
                tooth, _ = Tooth.objects.get_or_create(
                    tooth_number=number,
                    defaults={'tooth_name': str(number)}
                )
                case.selected_teeth.add(tooth)

        return case

    @staticmethod
    def auto_progress_case(case, user=None):
        """
        Automatically progress a case through its workflow stages.

        Args:
            case: The Case instance to progress
            user: The user triggering the progression

        Returns:
            (success, messages) tuple where messages is a list of transition results
        """
        try:
            results = workflow_automation.auto_progress_entity('case', case)

            if results:
                success_count = sum(1 for result, _ in results if result.value == 'success')
                total_count = len(results)

                logger.info(f"Case #{case.case_number} auto-progression: {success_count}/{total_count} transitions successful")

                return True, [message for _, message in results]
            else:
                return True, ["No automatic progressions available"]

        except Exception as e:
            logger.error(f"Error during case auto-progression: {e}", exc_info=True)
            return False, [f"Auto-progression failed: {str(e)}"]

    @staticmethod
    def get_valid_case_transitions(case):
        """
        Get all valid transitions for a case in its current state.

        Args:
            case: The Case instance

        Returns:
            List of valid transitions
        """
        try:
            return workflow_automation.get_valid_transitions('case', case)
        except Exception as e:
            logger.error(f"Error getting valid transitions for case: {e}")
            return []

    @staticmethod
    def move_case_to_next_stage(case, user=None, force=False):
        """
        Move a case to the next workflow stage.

        Args:
            case: The Case instance
            user: The user making the change
            force: Whether to force the stage transition

        Returns:
            (success, message) tuple
        """
        try:
            if not case.current_stage:
                return False, "Case has no current stage defined"

            # Get the next stage
            next_stage = case.workflow_template.stages.filter(
                order=case.current_stage.order + 1
            ).first()

            if not next_stage:
                # No next stage, try to complete the case
                context = TransitionContext(user=user, force=force)
                result, message = workflow_automation.transition_entity('case', case, 'completed', context)
                return result.value == 'success', message

            # Move to next stage
            old_stage = case.current_stage
            case.current_stage = next_stage
            case.next_stage = case.get_subsequent_stage()

            # Record stage completion
            case.record_stage_completion()

            # Create tasks for new stage
            case.create_stage_tasks()

            case.save()

            logger.info(f"Case #{case.case_number} moved from stage {old_stage.name} to {next_stage.name}")

            return True, f"Case moved to stage: {next_stage.name}"

        except Exception as e:
            logger.error(f"Error moving case to next stage: {e}", exc_info=True)
            return False, f"Failed to move to next stage: {str(e)}"

    @staticmethod
    def validate_case_workflow_state(case):
        """
        Validate the current workflow state of a case.

        Args:
            case: The Case instance to validate

        Returns:
            (is_valid, errors) tuple
        """
        try:
            errors = CentralizedValidationService.validate_business_operation(
                'workflow_validation',
                case=case
            )

            return len(errors) == 0, errors

        except Exception as e:
            logger.error(f"Error validating case workflow state: {e}")
            return False, [f"Validation error: {str(e)}"]


class TaskService:
    """Encapsulates business logic for Task operations."""

    @staticmethod
    def get_user_tasks(user):
        """Get tasks assigned to a user or in their departments."""
        if user.is_superuser:
            return Task.objects.all()

        user_departments = user.departments.all()
        return Task.objects.filter(
            Q(assigned_to=user) |
            Q(case__responsible_department__in=user_departments)
        ).distinct().order_by('-priority', 'deadline')

    @staticmethod
    def start_task(task, user):
        """
        Start a task using workflow automation.

        Args:
            task: The Task instance to start
            user: The user starting the task

        Returns:
            (success, message) tuple
        """
        try:
            # Use workflow automation for task transitions
            context = TransitionContext(
                user=user,
                reason=f"Task started by {user}",
                notify_stakeholders=True
            )

            result, message = workflow_automation.transition_entity('task', task, 'in_progress', context)

            if result.value == 'success':
                # If no one is assigned, assign the current user
                if not task.assigned_to:
                    task.assigned_to = user
                    task.save(update_fields=['assigned_to'])

                logger.info(f"Task #{task.id} started by {user}")
                return True, _("Task started successfully.")
            else:
                return False, message

        except WorkflowError as e:
            logger.error(f"Workflow error during task start: {e}")
            return False, str(e)
        except Exception as e:
            logger.error(f"Unexpected error during task start: {e}", exc_info=True)
            return False, f"System error: {str(e)}"

    @staticmethod
    def complete_task(task, user):
        """
        Complete a task using workflow automation.

        Args:
            task: The Task instance to complete
            user: The user completing the task

        Returns:
            (success, message) tuple
        """
        try:
            # Use workflow automation for task completion
            context = TransitionContext(
                user=user,
                reason=f"Task completed by {user}",
                notify_stakeholders=True
            )

            result, message = workflow_automation.transition_entity('task', task, 'completed', context)

            if result.value == 'success':
                logger.info(f"Task #{task.id} completed by {user}")

                # Notify case owner
                if task.case and task.case.dentist_user:
                    logger.info(f"Would create notification for user {task.case.dentist_user} about task completion")

                # Auto-progress the case if possible
                if task.case:
                    CaseService.auto_progress_case(task.case, user)

                return True, _("Task completed successfully.")
            else:
                return False, message

        except WorkflowError as e:
            logger.error(f"Workflow error during task completion: {e}")
            return False, str(e)
        except Exception as e:
            logger.error(f"Unexpected error during task completion: {e}", exc_info=True)
            return False, f"System error: {str(e)}"

    @staticmethod
    def assign_task(task, assigned_to, user):
        """
        Assign a task to a user.

        Args:
            task: The Task instance to assign
            assigned_to: The User to assign the task to
            user: The User making the assignment

        Returns:
            (success, message) tuple
        """
        if task.assigned_to == assigned_to:
            return True, _("Task already assigned to this user.")

        old_assigned_to = task.assigned_to
        task.assigned_to = assigned_to
        task.save(update_fields=['assigned_to'])

        # Log the action
        logger.info(f"Task #{task.id} reassigned from {old_assigned_to} to {assigned_to} by {user}")

        # Notify the newly assigned user
        if assigned_to:
            # Notification.objects.create(
            #     user=assigned_to,
            #     title=_('Task Assignment'),
            #     message=_(f'You have been assigned to task "{task.title}" for Case #{task.case.case_number}'),
            #     notification_type='task_assignment',
            #     reference_id=task.id
            # )
            # Temporarily commented out due to migration issues
            logger.info(f"Would create notification for user {assigned_to} about task assignment")

        return True, _("Task assigned successfully.")
