INFO 2025-05-24 18:04:25,853 autoreload 19352 8840 Watching for file changes with StatReloader
ERROR 2025-05-24 18:05:46,927 exceptions 8924 1500 Exception occurred: ['Invalid data']
NoneType: None
ERROR 2025-05-24 18:05:46,928 exceptions 8924 1500 Exception occurred: Unknown error
NoneType: None
ERROR 2025-05-24 18:05:47,052 validators 8924 1500 Validation error for case: '<' not supported between instances of 'str' and 'int'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 382, in validate_model_data
    field_errors = CentralizedValidationService._validate_fields(model_class, data)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 420, in _validate_fields
    validator(value)
    ~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\validators.py", line 395, in __call__
    if self.compare(cleaned, limit_value):
       ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\validators.py", line 429, in compare
    return a < b
           ^^^^^
TypeError: '<' not supported between instances of 'str' and 'int'
ERROR 2025-05-24 18:06:48,572 exceptions 3996 1568 Exception occurred: ['Invalid data']
NoneType: None
ERROR 2025-05-24 18:06:48,573 exceptions 3996 1568 Exception occurred: Unknown error
NoneType: None
ERROR 2025-05-24 18:06:48,695 validators 3996 1568 Validation error for case: '__proxy__' object has no attribute 'extend'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 411, in validate_model_data
    errors.setdefault(field, []).extend(field_error_list)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: '__proxy__' object has no attribute 'extend'
INFO 2025-05-24 18:10:18,073 basehttp 19352 20992 "GET / HTTP/1.1" 200 140233
INFO 2025-05-24 18:11:31,024 autoreload 19352 8840 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:11:32,415 autoreload 5728 11864 Watching for file changes with StatReloader
INFO 2025-05-24 18:11:35,217 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:37,843 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:40,222 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:42,260 basehttp 5728 20692 "GET / HTTP/1.1" 200 140233
INFO 2025-05-24 18:11:44,811 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:49,311 basehttp 5728 20692 "GET /case/case/15/ HTTP/1.1" 200 166812
INFO 2025-05-24 18:11:55,852 autoreload 5728 11864 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:11:57,286 autoreload 21180 6188 Watching for file changes with StatReloader
INFO 2025-05-24 18:12:12,302 basehttp 21180 20996 "GET /case/case/15/update/ HTTP/1.1" 200 221456
INFO 2025-05-24 18:12:20,222 autoreload 21180 6188 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:12:21,765 autoreload 4020 5876 Watching for file changes with StatReloader
INFO 2025-05-24 18:12:27,931 basehttp 4020 19144 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:12:29,845 basehttp 4020 19144 "GET /case/create/ HTTP/1.1" 200 222948
INFO 2025-05-24 18:12:42,809 autoreload 4020 5876 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:12:44,195 autoreload 21292 9656 Watching for file changes with StatReloader
INFO 2025-05-24 18:13:02,112 autoreload 21292 9656 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:13:03,638 autoreload 17420 3632 Watching for file changes with StatReloader
INFO 2025-05-24 18:13:48,176 basehttp 17420 5992 "POST /case/create/ HTTP/1.1" 302 0
INFO 2025-05-24 18:13:48,524 basehttp 17420 5992 "GET /case/case/16/ HTTP/1.1" 200 163413
INFO 2025-05-24 18:13:51,531 basehttp 17420 5992 "GET /case/list/ HTTP/1.1" 200 138335
ERROR 2025-05-24 18:16:34,629 status_synchronization 3192 22128 Error validating status consistency: No module named 'schedule'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\status_synchronization.py", line 320, in validate_status_consistency
    from schedule.models import Schedule
ModuleNotFoundError: No module named 'schedule'
ERROR 2025-05-24 18:16:34,635 dependency_management 3192 22128 Error getting task dependency chain: 'list' object has no attribute 'exclude'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\dependency_management.py", line 189, in get_task_dependency_chain
    incomplete_deps = direct_deps.exclude(status='completed')
                      ^^^^^^^^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'exclude'
ERROR 2025-05-24 18:16:34,641 exceptions 3192 22128 Exception occurred: Test error
NoneType: None
